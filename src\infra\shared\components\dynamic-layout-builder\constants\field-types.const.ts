import { FieldType, Field, FieldPermissionProfile, FieldOption } from '@domain/entities/field.entity';
import { TranslateService } from '@ngx-translate/core';

/**
 * Helper function để tạo FieldOption array từ string array
 * @param values Array các string values
 * @returns Array các FieldOption với _id được generate
 */
function createFieldOptions(values: string[]): FieldOption[] {
  return values.map((value, index) => ({
    _id: `temp-${Math.random().toString(36).substr(2, 9)}`,
    label: value,
    value: value
  }));
}


/**
 * Hàm tạo temporary ID cho field mới
 * Server sẽ dựa vào prefix 'temp-' để phân biệt field mới và field cũ
 */
export function generateTempFieldId(): string {
  const randomString = Math.random().toString(36).substring(2, 15);
  return `temp-${randomString}`;
}

/**
 * Constant chứa tất cả field types mặc định có thể sử dụng
 * Di chuyển từ method getDefaultFieldTypes() trong CoreLayoutBuilderService
 * Mỗi field có type để xác định loại - _id sẽ được generate khi tạo field mới
 * Descriptions sử dụng i18n keys với format FIELD_TYPES.{FIELD_TYPE}.DESCRIPTION
 */
export const DEFAULT_FIELD_TYPES: Field[] = [
  // Trường cơ bản
  {
    type: 'text',
    label: 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.TEXT',
    value: '',
    order: 1,
    description: 'FIELD_TYPES.TEXT.DESCRIPTION',
    permissionProfiles: [] // Sẽ được khởi tạo với giá trị mặc định khi tạo field
  },
  {
    type: 'number',
    label: 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.NUMBER',
    value: '',
    order: 2,
    description: 'FIELD_TYPES.NUMBER.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'email',
    label: 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.EMAIL',
    value: '',
    order: 3,
    description: 'FIELD_TYPES.EMAIL.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'phone',
    label: 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.PHONE',
    value: '',
    order: 4,
    description: 'FIELD_TYPES.PHONE.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'textarea',
    label: 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.TEXT_AREA',
    value: '',
    order: 5,
    description: 'FIELD_TYPES.TEXTAREA.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'date',
    label: 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.DATE',
    value: '',
    order: 6,
    description: 'FIELD_TYPES.DATE.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'datetime',
    label: 'DateTime',
    value: '',
    order: 7,
    description: 'FIELD_TYPES.DATETIME.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'file',
    label: 'File',
    value: '',
    order: 8,
    description: 'FIELD_TYPES.FILE.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'image',
    label: 'Image',
    value: '',
    order: 9,
    description: 'FIELD_TYPES.IMAGE.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'checkbox',
    label: 'DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.CHECKBOX',
    value: '',
    order: 10,
    description: 'FIELD_TYPES.CHECKBOX.DESCRIPTION',
    permissionProfiles: []
  },
  // Trường nâng cao
  {
    type: 'picklist',
    label: 'Picklist',
    value: '',
    order: 17,
    description: 'FIELD_TYPES.PICKLIST.DESCRIPTION',
    permissionProfiles: [],
    constraints: {
      options: createFieldOptions(['Option 1', 'Option 2', 'Option 3']),
      sortOrder: 'input',
      defaultValue: ''
    }
  },
  {
    type: 'multi-picklist',
    label: 'Multi Picklist',
    value: [],
    order: 18,
    description: 'FIELD_TYPES.MULTI_PICKLIST.DESCRIPTION',
    permissionProfiles: [],
    constraints: {
      options: createFieldOptions(['Option A', 'Option B', 'Option C']),
      sortOrder: 'input',
      defaultValue: []
    }
  },
  {
    type: 'url',
    label: 'URL',
    value: '',
    order: 19,
    description: 'FIELD_TYPES.URL.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'decimal',
    label: 'Decimal',
    value: '',
    order: 20,
    description: 'FIELD_TYPES.DECIMAL.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'currency',
    label: 'Currency',
    value: '',
    order: 21,
    description: 'FIELD_TYPES.CURRENCY.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'percent',
    label: 'Percent',
    value: '',
    order: 22,
    description: 'FIELD_TYPES.PERCENT.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'search',
    label: 'Search',
    value: '',
    order: 23,
    description: 'FIELD_TYPES.SEARCH.DESCRIPTION',
    permissionProfiles: []
  },
  {
    type: 'user',
    label: 'User',
    value: '',
    order: 24,
    description: 'FIELD_TYPES.USER.DESCRIPTION',
    permissionProfiles: []
  }
];

/**
 * Hàm lọc field types dựa trên danh sách supportedFieldTypes
 * @param supportedFieldTypes - Danh sách các FieldType được hỗ trợ
 * @returns Danh sách Field được filter
 */
export function getFilteredFieldTypes(supportedFieldTypes?: FieldType[]): Field[] {
  if (!supportedFieldTypes || supportedFieldTypes.length === 0) {
    return DEFAULT_FIELD_TYPES;
  }

  return DEFAULT_FIELD_TYPES.filter(fieldType =>
    supportedFieldTypes.includes(fieldType.type as FieldType)
  );
}

/**
 * Hàm lấy field type template dựa trên fieldType
 * @param fieldType - Type của field
 * @returns Field template hoặc null nếu không tìm thấy
 */
export function getFieldTypeById(fieldType: FieldType): Field | null {
  return DEFAULT_FIELD_TYPES.find(field => field.type === fieldType) || null;
}

export function getFieldTypeLabel(type: FieldType, translateService: TranslateService): string {
  const typeMap: { [key in FieldType]: string } = {
    // Trường cơ bản
    'text': translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.TEXT'),
    'number': translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.NUMBER'),
    'email': translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.EMAIL'),
    'phone': translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.PHONE'),
    'textarea': translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.TEXT_AREA'),
    'date': translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.DATE'),
    'datetime': translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.DATETIME'),
    'file': translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.FILE'),
    'image': translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.IMAGE'),
    'checkbox': translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.CHECKBOX'),
    'radio': translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.RADIO'),
    'select': translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.SELECT'),
    // Trường nâng cao
    'picklist': translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.PICKLIST'),
    'multi-picklist': translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.MULTI_PICKLIST'),
    'url': translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.URL'),
    'decimal': translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.DECIMAL'),
    'currency': translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.CURRENCY'),
    'percent': translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.PERCENT'),
    'search': translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.SEARCH'),
    'user': translateService.instant('DYNAMIC_LAYOUT_BUILDER.FIELD_TYPES.USER')
  };
  return typeMap[type] || type;
}

export function getFieldIcon(fieldType: FieldType): string {
  const iconMap: { [key in FieldType]: string } = {
    // Trường cơ bản
    'text': 'text_fields',
    'number': 'numbers',
    'email': 'email',
    'phone': 'phone',
    'textarea': 'notes',
    'date': 'calendar_today',
    'datetime': 'schedule',
    'file': 'attach_file',
    'image': 'image',
    'checkbox': 'check_box',
    'radio': 'radio_button_checked',
    'select': 'arrow_drop_down',
    // Trường nâng cao
    'picklist': 'arrow_drop_down',
    'multi-picklist': 'checklist',
    'url': 'link',
    'decimal': 'numbers',
    'currency': 'attach_money',
    'percent': 'percent',
    'search': 'search',
    'user': 'person'
  };
  return iconMap[fieldType] || 'help_outline';
}


export function createNewField(fieldType: FieldType, permissionProfiles: FieldPermissionProfile[], translateService: TranslateService) {
  const field: Field = DEFAULT_FIELD_TYPES.filter(f => f.type === fieldType)?.[0];

  if(field) {
    field._id = generateTempFieldId();
    field.label = getFieldTypeLabel(fieldType, translateService);
    field.permissionProfiles = permissionProfiles;
    return field;
  }
}