import { Injectable, signal, computed } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Field, FieldValue } from '@domain/entities/field.entity';
import { DynamicLayoutRendererFormData } from '../models/dynamic-layout-renderer.model';

/**
 * Interface định nghĩa validation result cho một field
 */
export interface FieldValidationResult {
  fieldId: string;
  isValid: boolean;
  errors: string[]; // Array of i18n keys for error messages
}

/**
 * Interface định nghĩa form state
 */
export interface FormState {
  isDirty: boolean; // Form đã được modify
  isValid: boolean; // Tất cả fields đều valid
  isLoading: boolean; // Đang trong quá trình save
  fieldValues: Record<string, FieldValue>; // Current field values
  fieldValidations: Record<string, FieldValidationResult>; // Validation results
}

/**
 * Service quản lý form state, validation, và data collection
 * Sử dụng Angular Signals để reactive state management
 * 
 * Features:
 * - Track form dirty state
 * - Validate fields theo constraints
 * - Collect form data để save
 * - Manage loading state
 */
@Injectable()
export class FormDataManagementService {
  // Private signals cho internal state
  private _isDirty = signal(false);
  private _isValid = signal(true);
  private _isLoading = signal(false);
  private _fieldValues = signal<Record<string, FieldValue>>({});
  private _fieldValidations = signal<Record<string, FieldValidationResult>>({});

  // Public computed signals
  readonly isDirty = this._isDirty.asReadonly();
  readonly isValid = this._isValid.asReadonly();
  readonly isLoading = this._isLoading.asReadonly();
  readonly fieldValues = this._fieldValues.asReadonly();
  readonly fieldValidations = this._fieldValidations.asReadonly();

  // Computed signal cho form state tổng hợp
  readonly formState = computed<FormState>(() => ({
    isDirty: this._isDirty(),
    isValid: this._isValid(),
    isLoading: this._isLoading(),
    fieldValues: this._fieldValues(),
    fieldValidations: this._fieldValidations()
  }));

  /**
   * Khởi tạo form với initial values
   * @param initialValues - Dữ liệu ban đầu từ DynamicLayoutRendererConfig.formValues
   * @param fields - Array of fields để validate initial values
   */
  initializeForm(initialValues: Record<string, FieldValue> = {}, fields: Field[] = []): void {
    this._fieldValues.set({ ...initialValues });
    this._isDirty.set(false);
    this._fieldValidations.set({});
    this._isLoading.set(false);

    // Validate tất cả fields với initial values
    this.validateAllFields(fields, initialValues);
  }

  /**
   * Cập nhật giá trị của một field
   * @param fieldId - ID của field (field._id)
   * @param value - Giá trị mới
   * @param field - Field object để validate
   */
  updateFieldValue(fieldId: string, value: FieldValue, field: Field): void {
    // Cập nhật field value
    const currentValues = this._fieldValues();
    const newValues = { ...currentValues, [fieldId]: value };
    this._fieldValues.set(newValues);

    // Mark form as dirty
    this._isDirty.set(true);

    // Validate field
    const validationResult = this.validateField(field, value);
    const currentValidations = this._fieldValidations();
    const newValidations = { ...currentValidations, [fieldId]: validationResult };
    this._fieldValidations.set(newValidations);

    // Update overall form validity
    this.updateFormValidity();
  }

  /**
   * Validate một field dựa trên constraints
   * @param field - Field object chứa constraints
   * @param value - Giá trị cần validate
   * @returns FieldValidationResult
   */
  private validateField(field: Field, value: FieldValue): FieldValidationResult {
    const errors: string[] = [];
    const fieldId = field._id || '';

    // Check required
    if ((field.isRequired || field.required) && this.isEmpty(value)) {
      errors.push('FORM_VALIDATION.FIELD_REQUIRED');
    }

    // Skip other validations if value is empty and not required
    if (this.isEmpty(value) && !(field.isRequired || field.required)) {
      return { fieldId, isValid: true, errors: [] };
    }

    // Type-specific validations
    switch (field.type) {
      case 'text':
      case 'email':
      case 'phone':
      case 'url':
        this.validateTextField(field, value as string, errors);
        break;
      case 'textarea':
        this.validateTextareaField(field, value as string, errors);
        break;
      case 'number':
      case 'decimal':
      case 'currency':
      case 'percent':
        this.validateNumberField(field, value as number, errors);
        break;
      case 'picklist':
        this.validatePicklistField(field, value as string, errors);
        break;
      case 'multi-picklist':
        this.validateMultiPicklistField(field, value as string[], errors);
        break;
      case 'file':
      case 'image':
        this.validateFileField(field, value, errors);
        break;
      // Add more field type validations as needed
    }

    return {
      fieldId,
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Validate text field (text, email, phone, url)
   */
  private validateTextField(field: Field, value: string, errors: string[]): void {
    if (!value) return;

    const constraints = field.constraints as any;
    if (!constraints) return;

    // Max length validation
    if (constraints.maxLength && value.length > constraints.maxLength) {
      errors.push('FORM_VALIDATION.TEXT_TOO_LONG');
    }

    // Email format validation
    if (field.type === 'email' && !this.isValidEmail(value)) {
      errors.push('FORM_VALIDATION.INVALID_EMAIL');
    }

    // Phone format validation
    if (field.type === 'phone' && constraints.maxDigits) {
      const digits = value.replace(/\D/g, '');
      if (digits.length > constraints.maxDigits) {
        errors.push('FORM_VALIDATION.PHONE_TOO_LONG');
      }
    }

    // URL format validation
    if (field.type === 'url' && !this.isValidUrl(value)) {
      errors.push('FORM_VALIDATION.INVALID_URL');
    }
  }

  /**
   * Validate textarea field
   */
  private validateTextareaField(field: Field, value: string, errors: string[]): void {
    if (!value) return;

    const constraints = field.constraints as any;
    if (!constraints) return;

    if (constraints.maxLength && value.length > constraints.maxLength) {
      errors.push('FORM_VALIDATION.TEXT_TOO_LONG');
    }
  }

  /**
   * Validate number field (number, decimal, currency, percent)
   */
  private validateNumberField(field: Field, value: number, errors: string[]): void {
    if (value === null || value === undefined) return;

    const constraints = field.constraints as any;
    if (!constraints) return;

    // Max digits validation
    if (constraints.maxDigits) {
      const digits = Math.abs(value).toString().replace('.', '').length;
      if (digits > constraints.maxDigits) {
        errors.push('FORM_VALIDATION.NUMBER_TOO_LONG');
      }
    }
  }

  /**
   * Validate picklist field
   */
  private validatePicklistField(field: Field, value: string, errors: string[]): void {
    if (!value) return;

    const constraints = field.constraints as any;
    if (!constraints || !constraints.picklistValues) return;

    if (!constraints.picklistValues.includes(value)) {
      errors.push('FORM_VALIDATION.INVALID_PICKLIST_VALUE');
    }
  }

  /**
   * Validate multi-picklist field
   */
  private validateMultiPicklistField(field: Field, value: string[], errors: string[]): void {
    if (!value || !Array.isArray(value)) return;

    const constraints = field.constraints as any;
    if (!constraints || !constraints.picklistValues) return;

    const invalidValues = value.filter(v => !constraints.picklistValues.includes(v));
    if (invalidValues.length > 0) {
      errors.push('FORM_VALIDATION.INVALID_PICKLIST_VALUE');
    }
  }

  /**
   * Validate file field
   */
  private validateFileField(field: Field, value: FieldValue, errors: string[]): void {
    // File validation logic - placeholder for now
    // Will be implemented based on file upload requirements
  }

  /**
   * Helper methods
   */
  private isEmpty(value: FieldValue): boolean {
    return value === null || value === undefined || value === '' || 
           (Array.isArray(value) && value.length === 0);
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Validate tất cả fields với current values
   * @param fields - Array of fields để validate
   * @param values - Current field values (optional, sử dụng current state nếu không có)
   */
  private validateAllFields(fields: Field[], values?: Record<string, FieldValue>): void {
    const currentValues = values || this._fieldValues();
    const validations: Record<string, FieldValidationResult> = {};

    // Validate từng field
    fields.forEach(field => {
      const fieldId = field._id || '';
      const fieldValue = currentValues[fieldId];
      const validationResult = this.validateField(field, fieldValue);
      validations[fieldId] = validationResult;
    });

    // Update validations
    this._fieldValidations.set(validations);

    // Update overall form validity
    this.updateFormValidity();
  }

  /**
   * Cập nhật overall form validity dựa trên tất cả field validations
   */
  private updateFormValidity(): void {
    const validations = this._fieldValidations();
    const isValid = Object.values(validations).every(v => v.isValid);
    this._isValid.set(isValid);
  }

  /**
   * Set loading state
   */
  setLoading(loading: boolean): void {
    this._isLoading.set(loading);
  }

  /**
   * Reset form về trạng thái ban đầu
   */
  resetForm(): void {
    this._isDirty.set(false);
    this._fieldValidations.set({});
    this.updateFormValidity();
  }

  /**
   * Build DynamicLayoutRendererFormData từ current form state
   * @param formId - Optional form ID
   * @returns DynamicLayoutRendererFormData
   */
  buildFormData(formId?: string): DynamicLayoutRendererFormData {
    const fieldValues = this._fieldValues();
    const values = Object.entries(fieldValues).map(([fieldId, value]) => ({
      fieldId,
      value
    }));

    return {
      formId,
      values
    };
  }

  /**
   * Get validation errors cho một field cụ thể
   * @param fieldId - ID của field
   * @returns Array of error i18n keys
   */
  getFieldErrors(fieldId: string): string[] {
    const validation = this._fieldValidations()[fieldId];
    return validation ? validation.errors : [];
  }

  /**
   * Check if field có errors
   * @param fieldId - ID của field
   * @returns true nếu field có errors
   */
  hasFieldErrors(fieldId: string): boolean {
    const validation = this._fieldValidations()[fieldId];
    return validation ? !validation.isValid : false;
  }
}
